<?php

namespace Database\Seeders\assessment\competency;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class AssignmentsSeeder extends Seeder
{
    protected int $school_id;
    protected string $connect = 'mysql_prod';
    private const SURVEYIDTOASSESSMENTID = [
        426 => 14,
        427 => 15,
        428 => 16,
        429 => 17,
        430 => 18,
        431 => 19,
        432 => 20,
    ];
    public function __construct($schoolId)
    {
        $this->school_id = $schoolId;
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 限制核心素养测评类型
        $assessmentIds = [14, 15, 16, 17, 18, 19, 20];
        $surveyIds = [426, 427, 428, 429, 430, 431, 432];

        // 查询学生测评记录
        $studentSurveyList = DB::connection($this->connect)
        ->table('assessment_schedules as schedules')
        ->join('assessment_tasks as tasks', 'schedules.id', '=', 'tasks.assessment_schedule_id')
        ->join('survey_user_session as session', function($join) {
            $join->on('session.survey_id', '=', 'tasks.old_survey_id')
                ->on('session.grade_id', '=', 'tasks.old_grade_id')
                ->on('session.times', '=', 'tasks.old_times');
        })
        ->whereIn('tasks.assessment_id', $assessmentIds)
        ->where('schedules.school_id', $this->school_id)
        ->where('session.school_id', $this->school_id)
        ->whereIn('session.survey_id', $surveyIds)
        ->where('session.is_delete', 0)
        ->where('session.is_abnormal', 0)
        ->where('session.time_error', 0)
        ->where('session.result', '!=', '')
        ->whereNotNull('session.result')
        ->select([
            'tasks.id',
            'tasks.assessment_id',
            'session.session_id',
            'session.member_id',
            'session.student_id',
            'session.create_time',
            'session.used_time',
            'session.result',
            'session.survey_id',
            'session.pdf_url',
            'session.school_id',
        ])
        ->get();

        // 优化查询效率
        $oldStudentIds = $studentSurveyList->pluck('student_id')->unique()->values()->toArray();
        $studentClassIdArr = DB::table('student_classes')
        ->whereIn('old_student_id', $oldStudentIds)
        ->select(['id', 'old_student_id'])
        ->pluck('id', 'old_student_id')
        ->toArray();

        // 使用集合方法优化数据处理
        $surveyToAssessmentMap = self::SURVEYIDTOASSESSMENTID; // 将常量赋值给变量
        $assignmentsData = [];
        $assignmentsData = $studentSurveyList->map(function($item) use ($studentClassIdArr,$surveyToAssessmentMap) {
        return [
            'old_session_id' => $item->session_id,
            'school_id' => $item->school_id,
            'assessment_task_id' => $item->id,
            'student_class_id' => $studentClassIdArr[$item->student_id] ?? 0,
            'student_id' => $item->member_id,
            'user_id' => $item->member_id,
            'duration' => $item->used_time,
            'results' => $item->result,
            'status' => 2,
            'assessment_id' => $surveyToAssessmentMap[$item->survey_id],
            'pdf_url' => $item->pdf_url,
            'created_at' => $item->create_time,
        ];
        })->toArray();

        // 使用 chunk 插入大量数据
        collect($assignmentsData)->chunk(1000)->each(function($chunk) {
        DB::table('assessment_task_assignments')->insert($chunk->toArray());
        });

        // 使用更好的日志记录方式
        Log::info('Competency assignments seeding completed', [
        'total_records' => count($assignmentsData),
        'last_task_id' => end($assignmentsData)['assessment_task_id'] ?? null
        ]);
    }



}
