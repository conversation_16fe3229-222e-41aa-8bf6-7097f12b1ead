<?php

namespace App\Services\School\Assessment\CompositeReport;

use App\Services\BaseService;
use App\Repositories\AssignmentRepository;
use App\Repositories\StudentRepository;
use App\Services\School\Assessment\CompositeReport\CompositeServiceFactory;
use App\Services\QwenService;
use App\Models\School\Assessment\AssessmentComprehensiveRecommendMajor;

class CareerService extends BaseService
{
    public function __construct(
        protected StudentRepository $studentRepository,
        protected AssignmentRepository $assignmentRepository,
        protected CompositeServiceFactory $compositeServiceFactory
    )
    {
        
    }

    /**
     * 生涯综合报告,仅高中有
     *
     * @param int $user_id 用户ID
     * @param int $school_id 学校ID
     * @return array 生涯综合报告数据
     */
    public function index($user_id, $school_id): array
    {
        $assessment_ids = [1,2,3,4,5];

        $latestAssignments = $this->assignmentRepository->getLatestAssignments($user_id, $school_id, $assessment_ids);

        $data_all = [];
        foreach ($latestAssignments as $assessment_id_info){
            $params['assessment_id'] = $assessment_id_info['assessment_id'];
            $params['assessment_task_assignment_id'] = $assessment_id_info['id'];
            
            $scoreService = $this->compositeServiceFactory->create($assessment_id_info['assessment_id']);
            
            $data = $scoreService->generateReport($params);

            switch ($assessment_id_info['assessment_id']){
                case 1:
                    $data_all['adaptation'] = $data;

                    $data_all['tag'][] = [
                        'title' => '学习生活适应性',
                        'description' => $data['standard_results']['total_score'] > 65 ? "从评估结果看，总体而言，你可能存在一定程度的心理不适，建议多与父母、老师、同学交流，如有需要，可联系学校心理老师寻求帮助。详见如下各方面情况。" : "从评估结果看，总体而言，你是积极、健康的，建议继续保持这种积极乐观向上的学习和生活态度。",
                    ];
                    break;
                case 2:
                    $data_all['intelligence'] = $data;

                    $data_all['tag'][] = [
                        'title' => '优势智能',
                        'description' => implode(' ', $data['intelligence_type']['strengths']),
                    ];
                    break;
                case 3:
                    $data_all['personality'] = $data;

                    $data_all['tag'][] = [
                        'title' => '性格评估',
                        'description' => $data['description'],
                    ];
                    break;
                case 4:
                    $data_all['interest'] = $data;
                    $dimensions = $data['standard_results']['dimensions'];
                    $three_top_name = array_column(array_slice($dimensions, 0, 3), 'name');

                    $data_all['tag'][] = [
                        'title' => '兴趣评估',
                        'code' => substr($data['standard_results']['code'], 0, 3),
                        'description' => implode(' ', $three_top_name),
                    ];
                    break;
                case 5:
                    $data_all['development'] = $data;
                    $development_levels = config('assessment.career.development_levels');

                    $data_all['tag'][] = [
                        'title' => '生涯发展水平',
                        'description' => '你当前的生涯发展水平得分为'.$data['standard_results']['total_score'].'分,生涯发展表现'.$development_levels[$data['standard_results']['total_score'] >= 20 ? 0 : 1],
                    ];
                    break;
            }
        }
        $data_all['info'] = $this->studentRepository->getStudentInfo($user_id);

        $majors = AssessmentComprehensiveRecommendMajor::where('user_id',$user_id)->get()->toArray();
        if(empty($majors)){
            //专业推荐列表，首次调用通义千问服务生成，并入库，后续从库里查询
            $service = new QwenService();
            $str = '我的生涯测评结果：兴趣是' . substr($data_all['interest']['standard_results']['code'],0,3) . ' 
            性格是'.$data_all['personality']['type'] . ' 
            优势智能是'. implode(',',$data_all['intelligence']['intelligence_type']['strengths']). '。
            中性智能是'. implode(',',$data_all['intelligence']['intelligence_type']['intermediate']).'。 
            劣势智能是'.implode(',',$data_all['intelligence']['intelligence_type']['needs_improvement']).'。 
            请根据我的生涯测评给我推荐8条专业方向及相应推荐理由和综合推荐指数（五星制）
            请返回json对象，不要空格，不要换行，不要转义字符，格式如下：
            [{"direction":"专业名称","reason":"简单描述推荐理由","level":"数字"},{....}]

            直接返回推荐数据，不要做其他说明
            ';

            $output = $service->qwenMax($str);

            $output_arr = json_decode($output,true);//返回的数据是字符串，需要转成数组
            $text = json_decode($output_arr['output']['text'],true);//text是字符串，需要转成数组

            $data = [
                'user_id'=>$user_id,
                'majors'=>$text,
            ];
            AssessmentComprehensiveRecommendMajor::create($data);
            $majors = AssessmentComprehensiveRecommendMajor::where('user_id',$user_id)->get()->toArray();
        }
        $data_all['majors'] = $majors[0]['majors'];

        return $data_all;
    }
}