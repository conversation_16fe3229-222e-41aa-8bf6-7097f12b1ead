<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class LoginRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'username' => 'required|string|min:4',
            'password' => 'required|string|min:5|regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/',
            'captcha' => 'required|string',
            'key' => 'required|string',
        ];
    }

    public function messages(): array
    {
        return [
            'username' => '用户名长度至少4位',
            'password' => '密码长度至少5位,必须包含大写字母、小写字母、数字',
            'captcha' => '验证码不能为空',
            'key' => 'key 不能为空',
        ];
    }
}
