<?php

namespace Database\Seeders\assessment\competency;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class AnswersSeeder extends Seeder
{
    protected int $school_id;

    protected string $connect = 'mysql_prod';

    public function __construct($schoolId)
    {
        $this->school_id = $schoolId;
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //限制测评类型
        $assessmentIds = [14, 15, 16, 17, 18, 19, 20];
        $surveyIds = [426, 427, 428, 429, 430, 431, 432];
        $surveyToAssessment = [
            '426'=>14,
            '427'=>15,
            '428'=>16,
            '429'=>17,
            '430'=>18,
            '431'=>19,
            '432'=>20,
        ];
        // 查询新老问题id的关系
        $assessmentCompetencyQuestionIds = DB::table('assessment_competency_questions')
        ->whereNotNull('old_question_id')
        ->select(['id', 'old_question_id'])
        ->pluck('id', 'old_question_id')
        ->toArray();

        // 查询assessment_task_assignment_id和session_id的关系
        $assessmentTaskAssignmentIds = DB::table('assessment_schedules as schedules')
        ->join('assessment_tasks as tasks', 'schedules.id', '=', 'tasks.assessment_schedule_id')
        ->join('assessment_task_assignments as assignments', 'tasks.id', '=', 'assignments.assessment_task_id')
        ->where('schedules.school_id', $this->school_id)
        ->select(['assignments.id', 'assignments.old_session_id'])
        ->pluck('assignments.id', 'assignments.old_session_id')
        ->toArray();

        // 查询老的答案表id和选项ABCD的关系
        $oldAnswerIds = DB::connection($this->connect)
        ->table('survey_question_answer')
        ->whereIn('survey_id', $surveyIds)
        ->select(['id', 'option'])
        ->pluck('option', 'id')
        ->toArray();
// dd($oldAnswerIds);
        // 优化学生答案查询
        $studentAnswerList = DB::connection($this->connect)
        ->table('assessment_schedules as schedules')
        ->join('assessment_tasks as tasks', 'schedules.id', '=', 'tasks.assessment_schedule_id')
        ->join('survey_user_session as session', function($join) {
            $join->on('session.survey_id', '=', 'tasks.old_survey_id')
                ->on('session.grade_id', '=', 'tasks.old_grade_id')
                ->on('session.times', '=', 'tasks.old_times');
        })
        ->join('survey_user_answer as answer', 'session.session_id', '=', 'answer.session')
        ->whereIn('tasks.assessment_id', $assessmentIds)
        ->where('schedules.school_id', $this->school_id)
        ->where('session.school_id', $this->school_id)
        ->whereIn('answer.survey_id', $surveyIds)
        ->where('session.is_delete', 0)
        ->where('session.is_abnormal', 0)
        ->where('session.time_error', 0)
        ->where('session.result', '!=', '')
        ->whereNotNull('session.result')
        ->whereNotNull('answer.input')
        ->select([
            'answer.input',
            'session.session_id',
            'session.member_id',
            'session.school_id',
            'session.survey_id',
        ])
        ->get()->toArray();
// dd($studentAnswerList);
        $answerData = [];
        $batchSize = 3000;

        foreach ($studentAnswerList as $item) {
            $input = json_decode($item->input, true);

            foreach ($input as $key => $value) {
                $answerOption = $this->getAnswerOption($value['answer_id'], $oldAnswerIds);
                $answerData[] = [
                    'assessment_task_assignment_id' => $assessmentTaskAssignmentIds[$item->session_id],
                    'student_id' => $item->member_id,
                    'assessment_competency_question_id' => $assessmentCompetencyQuestionIds[$key],
                    'answer' => $answerOption,
                    'school_id' => $item->school_id,
                    'assessment_id' => $surveyToAssessment[$item->survey_id],
                ];
            }

            // 当数据量达到1000条时进行插入
            if (count($answerData) >= $batchSize) {
                DB::table('assessment_competency_answers')->insert($answerData);
                $answerData = []; // 清空数组
            }
        }
        // 循环结束时answerData可能会有小于1000的数据没有insert
        if (!empty($answerData)) {
            DB::table('assessment_competency_answers')->insert($answerData);
        }
    }

    // 辅助方法处理答案选项
    private function getAnswerOption($answerId, $oldAnswerIds): string
    {
        if (strpos($answerId, ',') !== false) {
            return collect(explode(',', $answerId))
                ->map(function($id) use ($oldAnswerIds) {
                    return $oldAnswerIds[$id] ?? '';
                })
                ->filter()
                ->implode('');
        }

        return $oldAnswerIds[$answerId] ?? '';
    }

}
