<?php

namespace Database\Seeders\assessment;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;


class SchedulesSeeder extends Seeder
{
    protected int $school_id;
    protected string $connect = 'mysql_prod';
    public function __construct($schoolId)
    {
        $this->school_id = $schoolId;
    }
    public function run(): void
    {
      //  $schoolIdsStr = '122,123,1018,1019';
       // $schoolIds = explode(',', $schoolIdsStr);  // 将字符串转换为数组
        $survey_ids = [
            1, 18, 21, 28, 45, 32, 53, 72,
            234, 258, 257, 256, 255, 426, 427, 428,
            429, 430, 431, 432, 433, 471, 472, 473
        ];
        //新老测评ID转换
        $surveytoassessment_id = [
            1  => 1,
            18 => 2,
            21 => 3,
            28 => 4,
            45 => 5,
            32 => 6,
            53 => 7,
            72 => 8,
            234 => 9,
            258 => 10,
            257 => 11,
            256 => 12,
            255 => 13,
            426 => 14,
            427 => 15,
            428 => 16,
            429 => 17,
            430 => 18,
            431 => 19,
            432 => 20,
            471 => 21,
            472 => 22,
            473 => 23,
            433 => 24,
        ];
// 构建子查询，按 grade_id 和 times survey_id 分组
        $subQuery = DB::connection($this->connect)->table('survey_user_session')
            ->join('survey', 'survey.id', '=', 'survey_user_session.survey_id')
            ->select('grade_id','survey_id','title','times')
            ->where('survey_user_session.school_id', $this->school_id) // 使用 whereIn 来匹配 school_id 数组
            ->whereIn('survey_id', $survey_ids)
            ->where('times', '>', 0) //
            ->groupBy('grade_id','survey_id', 'times');

        $result =DB::connection($this->connect)->table('grade as g')
            ->select(
                'g.id',
                'g.name',
                'g.grade_name',
                'sub.grade_id',
                'sub.survey_id',
                'sub.title',
                'sub.times',
            )
            ->joinSub($subQuery, 'sub', function ($join) {
                $join->on('sub.grade_id', '=', 'g.id');
            })
            ->get()->toArray();
        // print_r($result);die;
        foreach ($result as $item) {
            $scheduleData = [
                'school_id' =>$this->school_id ,
                'old_times' => $item->times, // 使用数组访问符，因为 $result 是数组
                'name' => $item->name . $item->grade_name . $item->title . $item->survey_id . '测评计划' . $item->times,
                'open_time' => now()->toDateTimeString(),
                'close_time' => date('Y-m-d H:i:s', strtotime('+10 years')),
                'creator' => '管理员',
            ];
            $uniqueKey = $item->grade_id . '_' . $item->survey_id . '_' . $item->times;
            if (!isset($insertedSchedules[$uniqueKey])) {
                // 插入到 assessment_schedules 表并获取 ID
                $scheduleId = DB::table('assessment_schedules')->insertGetId($scheduleData);

                if ($scheduleId) {
                    // 存储已插入的 schedule_id 和对应的唯一键
                    $insertedSchedules[$uniqueKey] = $scheduleId;

                    // 构建 assessment_tasks 数据
                    $tasksData = [
                        'assessment_schedule_id' => $scheduleId,
                        'assessment_id' => $surveytoassessment_id[$item->survey_id],
                        'old_survey_id' => $item->survey_id,
                        'old_times' => $item->times,
                        'old_grade_id' => $item->grade_id,
                        // 其他可能的字段...
                    ];

                    // 插入到 assessment_tasks 表
                    DB::table('assessment_tasks')->insert($tasksData);
                } else {
                    // 处理插入失败的情况（理论上不应该发生，除非数据库有问题）
                }
            } else {
                $scheduleId = $insertedSchedules[$uniqueKey];
                // 仍然需要插入到 assessment_tasks 表，因为 survey_id 不同
                $tasksData = [
                    'assessment_schedule_id' => $scheduleId,
                    'assessment_id' => $surveytoassessment_id[$item->survey_id],
                    'old_survey_id' => $item->survey_id,
                    'old_times' => $item->times,
                    'old_grade_id' => $item->grade_id,

                ];

                DB::table('assessment_tasks')->insert($tasksData);
            }
        }
    }
}
