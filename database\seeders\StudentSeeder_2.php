<?php

namespace Database\Seeders;

use Illuminate\Database\QueryException;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use JetBrains\PhpStorm\NoReturn;

/**
 * 学生数据填充
 * User数据填充
 */
class StudentSeeder_2 extends Seeder
{
    protected int $school_id;
    protected string $connect = 'mysql_prod';

    public function __construct($schoolId)
    {
        $this->school_id = $schoolId;
        $this->connect = 'mysql_prod';
    }


    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $school_id = $this->school_id;
        //  获取学校对应的机构ID
        $orgID = DB::table('organizations')
            ->where('model_id', $school_id)
            ->where('model_type', 'school')
            ->value('id');

        // 检查是否已经处理过该学校
        $existingCount = DB::table('students')->where('school_id', $school_id)->count();
        if ($existingCount > 0) {
            echo "学校ID：{$school_id} 已存在 {$existingCount} 条学生数据，跳过处理\n";
            return;
        }

        $total = 0;
        $num = 1;
        $batchSize = 1000; // 保持原始批次大小

        try {
            // 学生用户数据迁移, 从student_convert表迁移数据
            DB::connection($this->connect)->table('student_convert')
                ->where('school_id', $school_id)
                ->orderBy('member_id')
                ->chunk($batchSize, function ($items) use ($orgID, $school_id, &$total, &$num) {
                    $user_data = [];
                    $student_data = [];

                    foreach ($items as $item) {
                        // 内联优化：减少方法调用开销，使用更快的字符串函数
                        $old_role_id = ('0,,0' !== $item->role_id && $item->role_id) ?
                            preg_replace('/0,(\d+),0/', '$1', $item->role_id) : null;

                        $gender = $item->gender > 1 ? 2 : 1;

                        // 使用 strtok 替代 explode，性能更好
                        $init_grade_id = $item->grade_sorts ? (int)strtok($item->grade_sorts, ',') : null;
                        $grade_year = $item->grade_years ? (int)strtok($item->grade_years, ',') : null;
                        $student_no = $item->student_nos ? strtok($item->student_nos, ',') : '';

                        // 优化日期计算，避免 strtotime
                        $date_start = $grade_year ? ($grade_year . '-09-01') : null;
                        $date_due = null;
                        if ($grade_year && $init_grade_id) {
                            if ($init_grade_id <= 5) {
                                $due_year = $grade_year + 5 - $init_grade_id;
                            } elseif ($init_grade_id <= 9) {
                                $due_year = $grade_year + 9 - $init_grade_id;
                            } else {
                                $due_year = $grade_year + 12 - $init_grade_id;
                            }
                            $date_due = $due_year . '-06-30';
                        }

                        // 用户User信息
                        $user_data[] = [
                            'id' => $item->member_id,
                            'organization_id' => $orgID,
                            'username' => $item->username,
                            'real_name' => $item->name,
                            'gender' => $gender,
                            'md5_password' => $item->password,
                            'role_id' => $old_role_id,
                            'creator' => '学生',
                        ];

                        // 学生Student信息
                        $student_data[] = [
                            'id' => $item->member_id,
                            'student_ids' => $item->student_ids,
                            'user_id' => $item->member_id,
                            'school_id' => $school_id,
                            'school_campus_id' => $item->school_district,
                            'student_name' => $item->name,
                            'gender' => $gender,
                            'student_no' => $student_no,
                            'school_no' => $student_no,
                            'init_grade_id' => $init_grade_id,
                            'grade_year' => $grade_year,
                            'date_start' => $date_start,
                            'date_due' => $date_due,
                        ];

                        $total++;
                    }

                    // 批量插入数据
                    if (!empty($user_data)) {
                        DB::table('users')->insertOrIgnore($user_data);
                        DB::table('students')->insertOrIgnore($student_data);

                        echo "学校ID：{$school_id}，迁移第{$num}批学生用户数据 " . date('Y-m-d H:i:s') . "\n";
                        $num++;
                    }
                });

            echo "学校ID：{$school_id}，共迁移{$total}条数据 " . date('Y-m-d H:i:s') . "\n";

//            // 批量更新用户表user_id（因为现在学生表和用户表的主键都是原来的member_id，所以这里不需要更新学生表的user_id了）
//            if($total>0){
//                DB::update('update students join users on students.id=users.openid set students.user_id=users.id
//                           where students.school_id=? and students.user_id is null ', [$school_id]);
//
//                echo "学校ID：" . $school_id . "，批量更新用户表user_id ".date('Y-m-d H:i:s') . "\n";
//            }

        } catch (QueryException $e) {
            DB::rollBack();
            dd($e->getMessage());
        }
    }

    /**
     * 处理单个学生数据，优化字符串操作
     */
    private function processStudentData($item, $orgID, $school_id): array
    {
        // 优化角色ID处理
        $old_role_id = null;
        if ($item->role_id && $item->role_id !== '0,,0') {
            if (preg_match('/0,(\d+),0/', $item->role_id, $matches)) {
                $old_role_id = $matches[1];
            }
        }

        // 优化年级和年份处理
        $init_grade_id = null;
        $grade_year = null;
        if ($item->grade_sorts) {
            $grade_sorts_array = explode(',', $item->grade_sorts);
            $init_grade_id = intval($grade_sorts_array[0]);
        }
        if ($item->grade_years) {
            $grade_years_array = explode(',', $item->grade_years);
            $grade_year = intval($grade_years_array[0]);
        }

        // 优化日期计算
        $date_start = null;
        $date_due = null;
        if ($grade_year) {
            $date_start = $grade_year . '-09-01';

            if ($init_grade_id) {
                // 简化毕业日期计算逻辑
                $due_year = $grade_year + $this->calculateGraduationOffset($init_grade_id);
                $date_due = $due_year . '-06-30';
            }
        }

        // 优化学号处理
        $student_no = '';
        if ($item->student_nos) {
            $student_nos_array = explode(',', $item->student_nos);
            $student_no = $student_nos_array[0];
        }

        $gender = $item->gender > 1 ? 2 : 1;

        return [
            'user' => [
                'id' => $item->member_id,
                'organization_id' => $orgID,
                'username' => $item->username,
                'real_name' => $item->name,
                'gender' => $gender,
                'md5_password' => $item->password,
                'role_id' => $old_role_id,
                'creator' => '学生',
            ],
            'student' => [
                'id' => $item->member_id,
                'student_ids' => $item->student_ids,
                'user_id' => $item->member_id,
                'school_id' => $school_id,
                'school_campus_id' => $item->school_district,
                'student_name' => $item->name,
                'gender' => $gender,
                'student_no' => $student_no,
                'school_no' => $student_no,
                'init_grade_id' => $init_grade_id,
                'grade_year' => $grade_year,
                'date_start' => $date_start,
                'date_due' => $date_due,
            ]
        ];
    }

    /**
     * 计算毕业年份偏移量
     */
    private function calculateGraduationOffset($init_grade_id): int
    {
        if ($init_grade_id <= 5) {
            return 5 - $init_grade_id;
        } elseif ($init_grade_id <= 9) {
            return 9 - $init_grade_id;
        } else {
            return 12 - $init_grade_id;
        }
    }

}
