<?php

namespace Database\Seeders;

use Illuminate\Database\QueryException;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use JetBrains\PhpStorm\NoReturn;

/**
 * 学生数据填充
 * User数据填充
 */
class StudentSeeder_2 extends Seeder
{
    protected int $school_id;
    protected string $connect = 'mysql_prod';

    public function __construct($schoolId)
    {
        $this->school_id = $schoolId;
        $this->connect = 'mysql_prod';
    }


    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $school_id = $this->school_id;
        //  获取学校对应的机构ID
        $orgID = DB::table('organizations')
            ->where('model_id', $school_id)
            ->where('model_type', 'school')
            ->value('id');

        // 检查是否已经处理过该学校
        $existingCount = DB::table('students')->where('school_id', $school_id)->count();
        if ($existingCount > 0) {
            echo "学校ID：{$school_id} 已存在 {$existingCount} 条学生数据，跳过处理\n";
            return;
        }

        $total = 0;
        $num = 1;
        $batchSize = 1000;

        try {
            // 学生用户数据迁移, 从student_convert表迁移数据(student_convert 表为取最新的学生数据转换表，中数据量较大，故采用chunk分批次迁移)
            $student_converts = DB::connection($this->connect)->table('student_convert')
                ->where('school_id', $school_id)
                ->orderBy('member_id')
                ->chunk($batchSize, function ($items) use ($orgID, $school_id, $batchSize, &$total, &$num) {
                    $user_data = [];
                    $student_data = [];
                    foreach ($items as $item) {
                        $old_role_id = '0,,0'!==$item->role_id ? preg_replace('/0,(\d+),0/', '$1', $item->role_id) : null;

                        // 用户User信息
                        $user_data[] = [
                            'id' => $item->member_id,
                            'organization_id' => $orgID,// 新增机构ID
                            'username' => $item->username,
                            'real_name' => $item->name,
                            // 'openid' => $item->member_id,// 临时存储原来用户表id，用于批量导入后批量更新Student表user_id
                            'gender' => $item->gender > 1 ? 2 : 1,
                            'md5_password' => $item->password,
                            'role_id' => $old_role_id,// 存原始角色id，在最后统一更新用户角色关系
                            'creator' => '学生',
                        ];

                        $init_grade_id = $item->grade_sorts ? intval(explode(',', $item->grade_sorts)[0]) : null;
                        $grade_year = $item->grade_years ? intval(explode(',', $item->grade_years)[0]) : null;
                        $date_start = null;
                        $date_due = null;
                        if ($grade_year) {
                            // 入学日期
                            $date_start = date('Y-m-d', strtotime($grade_year . '-09-01'));
                        }
                        if ($init_grade_id) {
                            // 根据年级区间推算毕业日期
                            if ($init_grade_id <= 5) {
                                $due_year = $grade_year + 5 - $init_grade_id;
                            } else {
                                if ($init_grade_id <= 9) {
                                    $due_year = $grade_year + 9 - $init_grade_id;
                                } else {
                                    $due_year = $grade_year + 12 - $init_grade_id;
                                }
                            }
                            // 毕业日期
                            $date_due = date('Y-m-d', strtotime($due_year . '-06-30'));
                        }

                        $student_no = $item->student_nos ? explode(',', $item->student_nos)[0] : '';
                        // 学生Student信息
                        $student_data[] = [
                            'id' => $item->member_id,//用原来用户表id
                            'student_ids' => $item->student_ids,//原学生表id组合
                            'user_id' => $item->member_id,
                            'school_id' => $school_id,
                            'school_campus_id' => $item->school_district,
                            'student_name' => $item->name,
                            'gender' => $item->gender > 1 ? 2 : 1,
                            'student_no' => $student_no,
                            'school_no' => $student_no,
                            'init_grade_id' => $init_grade_id,
                            'grade_year' => $grade_year,
                            'date_start' => $date_start,
                            'date_due' => $date_due,
                        ];

                        $total = $total+1;
                        // 当数据量达到1000条时进行插入
                        if (count($user_data) >= $batchSize) {
                            // 使用 insertOrIgnore 避免主键冲突
                            DB::table('users')->insertOrIgnore($user_data);
                            $user_data = []; // 清空数组
                            DB::table('students')->insertOrIgnore($student_data);
                            $student_data = []; // 清空数组

                            echo "学校ID：" . $school_id . "，迁移第". $num . "批学生用户数据 ".date('Y-m-d H:i:s') . "\n";

                            $num = $num+1;
                        }
                    }

                    // 最后一次插入剩余的数据
                    if(count($user_data) > 0){
                        // 使用 insertOrIgnore 避免主键冲突
                        DB::table('users')->insertOrIgnore($user_data);
                        DB::table('students')->insertOrIgnore($student_data);
                        echo "学校ID：" . $school_id . "，迁移第{$num}批学生用户数据 ".date('Y-m-d H:i:s') . "\n";
                    }

                });

            echo "学校ID：" . $school_id . "，共迁移{$total}条数据 ".date('Y-m-d H:i:s') . "\n";

//            // 批量更新用户表user_id（因为现在学生表和用户表的主键都是原来的member_id，所以这里不需要更新学生表的user_id了）
//            if($total>0){
//                DB::update('update students join users on students.id=users.openid set students.user_id=users.id
//                           where students.school_id=? and students.user_id is null ', [$school_id]);
//
//                echo "学校ID：" . $school_id . "，批量更新用户表user_id ".date('Y-m-d H:i:s') . "\n";
//            }

        } catch (QueryException $e) {
            dd($e->getMessage());
        }

    }

}
