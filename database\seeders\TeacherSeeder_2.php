<?php

namespace Database\Seeders;

use Illuminate\Database\QueryException;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

/**
 * 教师数据填充
 * User数据填充
 * 优化版本：减少N+1查询，使用事务，优化批量大小
 */
class TeacherSeeder_2 extends Seeder
{
    protected int $school_id;
    protected string $connect = 'mysql_prod';
    protected int $teacherType = 3;
    protected int $batchSize = 500; // 减少批量大小，提高内存效率

    /**
     * 构造函数
     * 
     * @param int $schoolId 学校ID
     */
    public function __construct($schoolId)
    {
        $this->school_id = $schoolId;
    }

    /**
     * Run the database seeds.
     * 执行教师数据填充
     */
    public function run(): void
    {
        $school_id = $this->school_id;
        $start_time = microtime(true);

        echo "开始处理学校ID：{$school_id} 的教师数据迁移...\n";

        //  获取学校对应的机构ID
        $orgID = DB::table('organizations')
            ->where('model_id', $this->school_id)
            ->where('model_type', 'school')
            ->value('id');

        if (!$orgID) {
            echo "错误：未找到学校ID {$school_id} 对应的机构信息\n";
            return;
        }

        $user_total = 0;
        $teacher_total = 0;
        $teacher_error_total = 0;
        $num = 1;

        try {
            // 获取学校角色列表
            $roleList = DB::connection($this->connect)->table('role')
                ->where('school_id', $school_id)
                ->where('step', 0)
                ->whereIn('type', [2, 3]) // 角色类型 2教务3教师
                ->get();

            // 处理每个角色下的账号
            foreach ($roleList as $role) {
                $old_role_id = $role->id;
                $old_role_id_str = "0,{$role->id},0";
                $type = $role->type;

                // 分批查询角色下的账号列表
                DB::connection($this->connect)->table('member')
                    ->where('role_id', $old_role_id_str)
                    ->where('step', 0)
                    ->orderBy('id')
                    ->chunk($this->batchSize, function ($members) use ($old_role_id, $orgID, $type, $school_id, &$teacher_error_total, &$user_total, &$teacher_total, &$num) {

                        // 预加载教师信息，避免N+1查询
                        $memberIds = $members->pluck('id')->toArray();
                        $teacherInfoMap = [];

                        if ($type == $this->teacherType) {
                            $teacherInfos = DB::connection($this->connect)->table('teacher')
                                ->whereIn('member_id', $memberIds)
                                ->where('step', 0)
                                ->get()
                                ->keyBy('member_id');
                            $teacherInfoMap = $teacherInfos->toArray();
                        }

                        $user_data = [];
                        $teacher_data = [];
                        $teacher_error_data = [];

                        foreach ($members as $member) {
                            // 教师和教务用户数据
                            $user_data[] = [
                                'id' => $member->id, // 原始ID
                                'organization_id' => $orgID, // 新增机构ID
                                'username' => $member->username,
                                'real_name' => $member->name,
                                'phone' => $member->mobile,
                                'gender' => $member->gender > 1 ? 2 : 1,
                                'md5_password' => $member->password,
                                'role_id' => $old_role_id, // 存原始角色id，在最后统一更新用户角色关系
                                'creator' => '教师',
                            ];
                            $user_total++;

                            // 如果是教师角色，准备教师数据
                            if ($type == $this->teacherType) {
                                // 从预加载的数据中获取教师信息
                                $teacher_info = $teacherInfoMap[$member->id] ?? null;

                                if ($teacher_info) {
                                    // 教师数据
                                    $teacher_data[] = [
                                        'id' => $member->id,
                                        'teacher_id' => $teacher_info->id, // 冗余字段，便于下次更新带班等关系表信息
                                        'user_id' => $member->id,
                                        'school_id' => $school_id,
                                        'school_campus_id' => $teacher_info->school_district, // 之前导入的是校区时，校区主键ID没有变更
                                        'teacher_name' => $teacher_info->name,
                                        'is_psychology_teacher' => $teacher_info->is_psych,
                                    ];
                                    $teacher_total++;
                                } else {
                                    // 教师错误数据
                                    $teacher_error_data[] = [$member->id];
                                    $teacher_error_total++;
                                }
                            }
                        }

                        // 使用事务批量插入数据
                        DB::beginTransaction();
                        try {
                            if (!empty($user_data)) {
                                DB::table('users')->insertOrIgnore($user_data);
                                echo "学校ID：{$school_id}，迁移第{$num}批教师/教务用户数据(" . count($user_data) . "条) " . date('Y-m-d H:i:s') . "\n";
                            }

                            // 批量插入教师数据
                            if (!empty($teacher_data)) {
                                DB::table('teachers')->insertOrIgnore($teacher_data);
                                echo "学校ID：{$school_id}，迁移第{$num}批教师数据(" . count($teacher_data) . "条) " . date('Y-m-d H:i:s') . "\n";
                            }

                            // 记录教师错误数据
                            if (!empty($teacher_error_data)) {
                                $teacher_error_data_str = implode(',', array_map(fn($item) => implode(',', $item), $teacher_error_data));
                                echo "学校ID：{$school_id}，迁移第{$num}批教师错误数据用户ID：{$teacher_error_data_str}\n";
                            }

                            DB::commit();
                            $num++;
                        } catch (QueryException $e) {
                            DB::rollBack();
                            echo "批量插入失败：" . $e->getMessage() . "\n";
                            throw $e; // 重新抛出异常，停止执行
                        }
                    });
            }

            $end_time = microtime(true);
            $execution_time = round($end_time - $start_time, 2);

            echo "学校ID：{$school_id} 数据迁移完成！\n";
            echo "- 共迁移 {$user_total} 条用户数据\n";
            echo "- 成功迁移 {$teacher_total} 条教师数据\n";
            echo "- {$teacher_error_total} 条教师数据未找到\n";
            echo "- 执行时间：{$execution_time} 秒\n";
            echo "- 完成时间：" . date('Y-m-d H:i:s') . "\n";

            // 强制垃圾回收，释放内存
            gc_collect_cycles();

        } catch (QueryException $e) {
            echo "迁移过程发生错误：" . $e->getMessage() . "\n";
            throw $e;
        }
    }
}