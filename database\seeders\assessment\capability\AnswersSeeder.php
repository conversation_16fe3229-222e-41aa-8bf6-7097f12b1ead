<?php

namespace Database\Seeders\assessment\capability;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
ini_set('memory_limit', '2048M');
class AnswersSeeder extends Seeder
{

    protected int $school_id;

    protected string $connect = 'mysql_prod';

    public function __construct($schoolId)
    {
        $this->school_id = $schoolId;
    }
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        //限制测评类型
        $assessmentIds = [9,10,11,12,13];
        $surveyIds = [234,258,257,256,255];
        $surveyToAssessment = [
            '234'=>9,
            '258'=>10,
            '257'=>11,
            '256'=>12,
            '255'=>13,
        ];
        // 查询老的答案表id和选项ABCD的关系
        $oldAnswerIds = DB::connection($this->connect)
        ->table('survey_question_answer')
        ->whereIn('survey_id', $surveyIds)
        ->select(['id', 'option'])
        ->pluck('option', 'id')
        ->toArray();
        // 查询新老问题id的关系
        $assessmentCapabilityQuestionIds = DB::table('assessment_capability_questions')
            ->whereNotNull('old_question_id')
            ->select(['id', 'old_question_id'])
            ->pluck('id', 'old_question_id')
            ->toArray();

        // 查询assessment_task_assignment_id和session_id的关系
        $assessmentTaskAssignmentIds = DB::table('assessment_schedules as schedules')
            ->join('assessment_tasks as tasks', 'schedules.id', '=', 'tasks.assessment_schedule_id')
            ->join('assessment_task_assignments as assignments', 'tasks.id', '=', 'assignments.assessment_task_id')
            ->where('schedules.school_id', $this->school_id)
            ->select(['assignments.id', 'assignments.old_session_id'])
            ->pluck('assignments.id', 'assignments.old_session_id')
            ->toArray();

        $studentAnswerList = DB::connection($this->connect)
            ->table('assessment_schedules as schedules')
            ->join('assessment_tasks as tasks', 'schedules.id', '=', 'tasks.assessment_schedule_id')
            ->join('survey_user_session as session', function($join) {
                $join->on('session.survey_id', '=', 'tasks.old_survey_id')
                    ->on('session.grade_id', '=', 'tasks.old_grade_id')
                    ->on('session.times', '=', 'tasks.old_times');
            })
            ->join('survey_user_answer as answer', 'session.session_id', '=', 'answer.session')
            ->whereIn('tasks.assessment_id', $assessmentIds)
            ->where('schedules.school_id', $this->school_id)
            ->where('session.school_id', $this->school_id)
            ->whereIn('answer.survey_id', $surveyIds)
            ->where('session.is_delete', 0)
            ->where('session.is_abnormal', 0)
            ->where('session.time_error', 0)
            ->where('session.result', '!=', '')
            ->whereNotNull('session.result')
            ->whereNotNull('answer.input')
            ->select([
                'answer.input',
                'session.session_id',
                'session.member_id',
                'session.school_id',
                'session.survey_id',
            ])
            ->get()->toArray();

        $batchSize = 3000; // 每批处理的数据量
        $answerData = [];

        foreach ($studentAnswerList as $item) {
            $input = json_decode($item->input, true);

            foreach ($input as $key => $value) {
                $answerData[] = [
                    'assessment_task_assignment_id' => $assessmentTaskAssignmentIds[$item->session_id],
                    'student_id' => $item->member_id,
                    'assessment_capability_question_id' => $assessmentCapabilityQuestionIds[$key],
                    'answer' => $oldAnswerIds[$value['answer_id']],
                    'school_id' => $item->school_id,
                    'assessment_id' => $surveyToAssessment[$item->survey_id],
                ];

                // 如果达到批次大小，则插入数据库并清空数组
                if (count($answerData) >= $batchSize) {
                    DB::transaction(function () use ($answerData) {
                        DB::table('assessment_capability_answers')->insert($answerData);
                    });
                    $answerData = []; // 清空数组以释放内存
                }
            }
        }
        if (!empty($answerData)) {
            DB::transaction(function () use ($answerData) {
                DB::table('assessment_capability_answers')->insert($answerData);
            });
        }
    }
}