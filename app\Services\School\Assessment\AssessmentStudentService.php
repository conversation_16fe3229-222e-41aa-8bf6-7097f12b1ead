<?php

namespace App\Services\School\Assessment;

use App\Repositories\ScheduleRepository;
use App\Repositories\AssignmentRepository;
use App\Services\BaseService;
use Illuminate\Database\Eloquent\Builder;
use App\Services\School\Assessment\CompositeReport\CompetencyService;
use App\Services\School\Assessment\CompositeReport\CareerService;

class AssessmentStudentService extends BaseService
{
    public function __construct(
        protected ScheduleRepository $scheduleRepository, 
        protected CompetencyService $competencyService, 
        protected CareerService $careerService, 
        protected AssignmentRepository $assignmentRepository
    )
    { 
    }
    /**
     * 获取学生的测评计划列表
     * 
     * @param int $studentId 学生ID
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function getStudentSchedules(int $studentId, int $schoolId, int $type)
    {
        return $this->scheduleRepository->getStudentSchedules($studentId, $schoolId, $type);
    }

    /**
     * 获取学生的测评计划列表
     * 
     * @param int $studentId 学生ID
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function getStudentTasks(int $studentId, int $schoolId, int $scheduleId): Builder
    {
        return $this->scheduleRepository->getStudentTasks($studentId,$schoolId,$scheduleId);
    }

    /**
     * 获取学生的测评计划过期时间
     * 
     * @param int $studentId 学生ID
     * @return array
     */
    public function getScheduleCloseTime(int $id): string
    {
        $schedule_info = $this->scheduleRepository->getScheduleInfo($id,'close_time');

        return $schedule_info['close_time'];
    }
    
    /**
     * 获取学生的测评计划名称
     * 
     * @param int $id 测评计划ID
     * @return string
     */
    public function getScheduleName(int $id): string
    {
        $schedule_info = $this->scheduleRepository->getScheduleInfo($id,'name');
        
        return $schedule_info['name'];
    }
    
    /**
     * 获取测评计划名称
     * 
     * @param int $schedule_id 计划ID
     * @return array
     */
    public function getAssessmentInfo(int $id): array
    {
        return $this->scheduleRepository->getAssessmentInfo($id);
    }
    
    /**
     * 获取测评计划名称
     * 
     * @param int $schedule_id 计划ID
     * @return array
     */
    public function compositeReport(string $module, int $user_id, int $school_id): array
    {
        switch ($module) {
            case 'career':
                return $this->careerService->index($user_id, $school_id);
                break;
            case 'competency':
                return $this->competencyService->index($user_id, $school_id);
                break;
            default:
                return [];
        }
    }
    
    /**
     * 获取测评数据
     * 
     * @param int $assessment_id 测评ID
     * @return array
     */
    public function tag(int $user_id, int $school_id): array
    {
        //生涯和五力部分
        $latestAssignments = $this->assignmentRepository->getLatestAssignments($user_id, $school_id, range(1,23));

        $tag = [];
        foreach ($latestAssignments as $assignment) {
            switch ($assignment['assessment_id']) {
                case 1:
                    $tag['adaptation'] = [
                        'title' => '学习生活适应性',
                        'description' => $assignment['standard_results']['total_score'] > 65 ? "从评估结果看，总体而言，你可能存在一定程度的心理不适，建议多与父母、老师、同学交流，如有需要，可联系学校心理老师寻求帮助。详见如下各方面情况。" : "从评估结果看，总体而言，你是积极、健康的，建议继续保持这种积极乐观向上的学习和生活态度。",
                    ];
                    break;
                case in_array($assignment['assessment_id'], [2,6]):
                    $dimensions = $assignment['standard_results']['dimensions'];
                    array_multisort(array_column($dimensions,'score'), SORT_DESC, $dimensions);
                    $advantages_count = $assignment['standard_results']['each_level_count']['advantages'];
                    $advantages_name = array_column(array_slice($dimensions, 0, $advantages_count), 'name');
                    $tag['intelligence'] = [
                        'title' => '优势智能',
                        'description' => implode(' ', $advantages_name),
                    ];
                    break;
                case 3:
                    $personality_code_mapping = config('assessment.career.personality_code_mapping');
                    $reverse_mapping = array_flip($personality_code_mapping);
                    
                    $code = $assignment['standard_results']['code'];
                    $letters = str_split($code);

                    // 转换每个字母为对应的汉字
                    $result = array_map(function($letter) use ($reverse_mapping) {
                        return $reverse_mapping[$letter] ?? $letter; // 如果找不到映射则保留原字母
                    }, $letters);

                    $tag['personality'] = [
                        'title' => '性格评估',
                        'code' => $code,
                        'description' => implode(' ', $result),
                    ];
                    break;
                case in_array($assignment['assessment_id'], [4,8]):
                    $dimensions = $assignment['standard_results']['dimensions'];
                    $three_top_name = array_column(array_slice($dimensions, 0, 3), 'name');

                    $tag['interest'] = [
                        'title' => '兴趣评估',
                        'code' => substr($assignment['standard_results']['code'], 0, 3),
                        'description' => implode(' ', $three_top_name),
                    ];
                    break;
                case in_array($assignment['assessment_id'], [5,7]):
                    $development_levels = config('assessment.career.development_levels');
                    $tag['development'] = [
                        'title' => '生涯发展水平',
                        'description' => '你当前的生涯发展水平得分为'.$assignment['standard_results']['total_score'].'分,生涯发展表现'.$development_levels[$assignment['standard_results']['total_score'] >= 20 ? 0 : 1],
                    ];
                    break;
                case 9:
                    $tag['learning_ability'] = $this->fiveForceTag($assignment['standard_results']['total_score'], 'learning_ability', '学习力');
                    break;
                case 10:
                    $tag['critical_thinking'] = $this->fiveForceTag($assignment['standard_results']['total_score'], 'critical_thinking', '批判性思维');
                    break;
                case 11:
                    $tag['problem_solving'] = $this->fiveForceTag($assignment['standard_results']['total_score'], 'problem_solving', '问题解决能力');
                    break;
                case 12:
                    $tag['creative_thinking'] = $this->fiveForceTag($assignment['standard_results']['total_score'], 'creative_thinking', '创造性思维');
                    break;
                case 13:
                    $tag['communication_collaboration'] = $this->fiveForceTag($assignment['standard_results']['total_score'], 'communication_collaboration', '沟通与合作');
                    break;
            }
        }

        //学科素养部分


        return $tag;
    }
    
    /**
     * 计算维度等级
     * 
     * @param float $score 分数
     * @param float $low 低分阈值
     * @param float $high 高分阈值
     * @return array 等级信息数组
     */
    protected function fiveForceTag(float $score, string $type, string $title): array
    {
        $ability = config('assessment.capability.'.$type.'.category');
        $low = $ability[count($ability)-1]['low'];
        $high = $ability[count($ability)-1]['high'];

        if($score < $low){
            $str = '等级评分为：L1,表现欠佳';
        }elseif($score < $high){
            $str = '等级评分为：L2,表现一般';
        }else{
            $str = '等级评分为：L3,表现优秀';
        }
        
        return [
            'title' => $title,
            'description' => $str,
        ];
    }

    /**
     * 获取测评数据
     * 
     * @param int $assessment_id 测评ID
     * @return array
     */
    public function assessmentData(int $assessment_id, int $user_id, int $school_id): array
    {
        $latestAssignments = $this->assignmentRepository->getLatestAssignments($user_id, $school_id, [$assessment_id]);

        switch ($assessment_id) {
            case $assessment_id == 17 || $assessment_id == 19:
                return array_merge([$latestAssignments[0]['standard_results']['dimension_ability']], $latestAssignments[0]['standard_results']['dimension_tendency']);
                break;
            default:
                return $latestAssignments[0]['standard_results']['dimensions'];
        }
    }

}