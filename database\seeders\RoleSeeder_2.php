<?php

namespace Database\Seeders;

use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

/**
 * 添加角色
 * 添加角色菜单
 */
class RoleSeeder_2 extends Seeder
{
    protected int $school_id;
    protected string $connect = 'mysql_prod';

    public function __construct($schoolId)
    {
        $this->school_id = $schoolId;
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 获取学校对应的机构ID
        $orgID = DB::table('organizations')
            ->where('model_id', $this->school_id)
            ->where('model_type', 'school')
            ->value('id');
        if (empty($orgID)) {
            dd('学校ID：' . $this->school_id . ' 对应的机构ID为空');
        }

        // 按学校查询下面的角色和菜单信息
        DB::connection($this->connect)->table('role')
            ->select('role.id', 'role.name', 'role.type')
            ->where('role.school_id', $this->school_id)
            ->where('role.step', '>=', 0)
            ->get()
            ->each(function (&$item) use ($orgID) {
                // 新增角色
                $roleData = [
                    'organization_id' => $orgID,
                    'name' => $item->name,
                    'guard_name' => 'user',
                    'type' => $item->type,
                    'role_id' => $item->id,// 存储原表角色ID，用于后续用户匹配角色role_id
                    'created_at' => date('Y-m-d H:i:s', time()),
                    'updated_at' => date('Y-m-d H:i:s', time()),
                ];

                DB::table('roles')->insert($roleData);
            });
    }

}
