<?php

namespace App\Http\Controllers;

use App\Exceptions\BusinessException;
use App\Http\Requests\CreateThirdPartyUserRequest;
use App\Http\Requests\GetUserByOpenidRequest;
use App\Http\Requests\ModifyPasswordRequest;
use App\Models\User;
use App\Services\RoleService;
use App\Services\UserService;
use App\Services\DataSync\TeacherSyncService;
use App\Traits\CrudOperations;
use App\Traits\PasswordTrait;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;
use Tymon\JWTAuth\Exceptions\JWTException;

class UserController extends Controller
{
    use PasswordTrait;
    use CrudOperations;

    protected string $model = User::class;

    protected $userService;
    protected $roleService;

    // 通过依赖注入注入 User 模型
    public function __construct(UserService $userService, RoleService $roleService)
    {
        $this->userService = $userService;
        $this->roleService = $roleService;
    }

    // ==================================== 登录用户所需接口 begin ====================================
    // 获取当前登录用户角色菜单
    public function getUserMenus(Request $request)
    {
        return $this->success($this->userService->getLoginUserMenus($request));
    }

    /**
     * 当前登录用户信息
     */
    public function getLoginUserInfo()
    {
        $user = $this->userService->getLoginUserInfo();
        return $this->success($user);
    }

    /**
     * 通过 token 获取当前用户详细信息
     * 包含角色信息、教师所带班级、学生归属班级等
     */
    public function getCurrentUserDetail(Request $request)
    {
        try {
            $userInfo = $this->userService->getCurrentUserDetail($request);
            return $this->success($userInfo, '获取用户信息成功');
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getCode());
        }
    }

    /**
     * 生成用户 Token
     */
    public function generateUserToken(Request $request)
    {
        try {
            $data = $this->userService->generateUserToken($request);
            return $this->success($data, 'Token 生成成功');
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getCode());
        }
    }

    /**
     * 当前登录用户的机构信息
     */
    public function getLoginUserOrgInfo(Request $request)
    {
        $res = $this->userService->getLoginUserOrgInfo($request);
        return $this->success($res);
    }

    /**
     * 无登录状态获取机构及配置信息
     */
    public function getOrganizationInfo(Request $request)
    {
        $res = $this->userService->getOrganizationInfo($request);
        return $this->success($res);
    }

    // 修改密码
    public function modifyPassword(ModifyPasswordRequest $request)
    {
        $this->userService->modifyPassword($request);
        return $this->message('密码修改成功');
    }

    /**
     * 用户登出
     */
    public function logOut(): JsonResponse
    {
        try {
            Auth::logout();
            return $this->message('Successfully logged out');
        } catch (JWTException $exception) {
            return $this->error('Sorry,the user cannot be logged out');
        }
    }

    /**
     * 根据openid获取用户登录信息
     */
    public function getUserByOpenid(GetUserByOpenidRequest $request)
    {
        try {
            $openid = $request->input('openid');
            $data = $this->userService->getUserByOpenid($openid);
            return $this->success($data, '获取用户信息成功');
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getCode());
        }
    }

    // ==================================== 登录用户所需接口 end ====================================

    // ==================================== 用户管理所需接口 begin ====================================
    // 用户列表
    public function index(Request $request)
    {
        $organization_id = $this->roleService->getOrganizationId($request);
        $query = $this->userService->listBuilder($request, $organization_id);
        $cnt = $query->count();
        $list = $query->pagination()->orderBy('id', 'desc')->get()->each(function ($item) {
            $item->role_names = $item->roles->pluck('name')->join(',');
            unset($item->roles);
        });
        return $this->paginateSuccess($list, $cnt);
    }

    public function show($id)
    {
        try {
            $user = $this->userService->getUserDetail($id);
            return $this->success($user);
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getCode());
        }
    }

    // 创建用户
    public function store(Request $request)
    {
        try {
            DB::beginTransaction();

            // 校验参数
            $validator = Validator::make($request->all(), [
                'password' => 'required|string|min:8|regex:/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d).+$/',
            ], [
                'password.regex' => '密码至少8位,必须包含大写字母、小写字母、数字',
                'password.required' => '密码至少8位,必须包含大写字母、小写字母、数字',
                'password.min' => '密码至少8位,必须包含大写字母、小写字母、数字',
            ]);
            if ($validator->fails()) {
                return $this->error('新密码至少8位,必须包含大写字母、小写字母、数字');
            }

            // 获取并过滤请求数据
            $data = filterRequestData("users");
            $password = $request->input('password');

            // 处理教务用户创建的特殊参数
            if ($request->has('real_name')) {
                $data['real_name'] = $request->input('real_name');
            }
            if ($request->has('gender')) {
                $data['gender'] = $request->input('gender');
            }

            $organization_id = $this->roleService->getOrganizationId($request);
            // 调用模型方法创建用户
            $user = $this->userService->createUser($data, $password, $organization_id, $request->user()->real_name);

            // 保存中间表 角色
            $user->roles()->sync($request->input('roles'));

            // 检查是否需要同步到老师接口（教务角色）
            $dataSyncService = app(\App\Services\DataSync\DataSyncService::class);
            if ($dataSyncService->isSyncEnabled()) {
                $teacherSyncService = app(TeacherSyncService::class);
                $syncResult = $teacherSyncService->syncAdminUserIfNeeded($user, $request);
            }

            DB::commit();

            return $this->message("初始密码为：{$password}");
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('用户创建失败');
        }
    }

    // 更新用户
    public function update(Request $request, string $id)
    {
        try {
            $result = $this->userService->updateUser($request, $id);
            if($result) {
                return $this->message("修改成功");
            } else {
                return $this->error('修改失败');
            }
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getCode());
        }
    }

    public function destroy(Request $request, $id)
    {
        try {
            $result = $this->userService->destroy($request, $id);
            if($result) {
                return $this->message("删除成功");
            } else {
                return $this->error('删除失败');
            }
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getCode());
        }
    }


    // 修改用户状态
    public function changeState(Request $request, $id)
    {
        try {
            $result = $this->userService->changeUserState($request, $id);
            if($result) {
                return $this->message('修改成功');
            } else {
                return $this->error('修改失败');
            }
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getCode());
        }
    }

    /**
     * 【远播教育】创建用户接口
     */
    public function createThirdPartyUser(CreateThirdPartyUserRequest $request)
    {
        try {
            DB::beginTransaction();

            $openid = 'yb_'. $request->input('phone') . '_' . $request->input('student_id');
            if ($request->input('openid') !== $openid) {
                return $this->error('学生openid不合法');
            }

            // 准备用户数据
            $userData = [
                'username' => $request->input('openid'),
                'real_name' => $request->input('real_name'),
                'gender' => $request->input('gender'),
                'phone' => $request->input('phone'),
                'openid' => $request->input('openid'),
            ];

            $user = $this->userService->createThirdPartyUser($userData, 
                $request->user()->organization_id, $request->user()->real_name);

            DB::commit();

            return $this->success([
                'user_id' => $user->id
            ]);
        } catch (\Exception $e) {
            DB::rollBack();
            return $this->error('用户创建失败');
        }
    }

    // 重置密码
    public function resetPassword(Request $request, string $id)
    {
        try {
            $password = $this->userService->resetUserPassword($request, $id);
            return $this->success($password,"重置后新密码为：{$password}");
        } catch (BusinessException $e) {
            return $this->error($e->getMessage(), $e->getCode());
        }
    }



    // ==================================== 用户管理所需接口 end ====================================
}
