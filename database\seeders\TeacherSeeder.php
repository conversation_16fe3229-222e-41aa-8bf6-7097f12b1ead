<?php

namespace Database\Seeders;

use Illuminate\Database\QueryException;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

/**
 * 教师数据填充
 * User数据填充
 */
class TeacherSeeder extends Seeder
{
    protected int $school_id;
    protected string $connect = 'mysql_prod';

    public function __construct($schoolId)
    {
        $this->school_id = $schoolId;
    }
    protected int $teacherType = 3;
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $school_id = $this->school_id;
        //  获取学校对应的机构ID
        $orgID = DB::table('organizations')
            ->where('model_id', $this->school_id)
            ->where('model_type', 'school')
            ->value('id');
        // 获取学校角色列表
        $roleList = DB::connection($this->connect)->table('role')
            ->where('school_id', $school_id)
            ->where('step', 0)
            ->whereIn('type', [2, 3]) // 角色类型 2教务3教师
            ->get();
        // 查询角色下的账号列表
        $roleList->each(function ($item) use($orgID, $school_id){
            $old_role_id = $item->id;
            $old_role_id_str = '0,'.$item->id.',0';
            $type = $item->type;
            $accountList = DB::connection($this->connect)->table('member')
                ->where('role_id', $old_role_id_str)
                ->where('step', 0)
                ->get()->each(function ($item_member) use($old_role_id, $orgID, $type, $school_id) {
                    // 教师和教务用户数据
                    $user_data = [
                        'id' => $item_member->id,// 原始ID***
                        'organization_id' => $orgID,// 新增机构ID
                        'username' => $item_member->username,
                        'real_name' => $item_member->name,// 学生姓名 $member_info->student_name,
                        'phone' => $item_member->mobile,
                        'gender' => $item_member->gender > 1 ? 2 : 1,
                        'md5_password' => $item_member->password,
                        'role_id' => $old_role_id, // 存原始角色id，在最后统一更新用户角色关系
//                        'created_at' => date('Y-m-d H:i:s',time()),
//                        'updated_at' => date('Y-m-d H:i:s',time()),
                    ];
                    // 存储用户数据，获取user_id
                    try{
                        $user_id = DB::table('users')->insertGetId($user_data);
//                        DB::table('users')->insert($user_data);
                        // 教师角色查询相关业务数据
                        if($type == $this->teacherType){
                            // 查询账号对应的教师信息
                            $teacher_info = DB::connection($this->connect)->table('teacher')
                                ->where('member_id', $item_member->id)
                                ->where('step', 0)
                                ->first();
                            if($teacher_info){
                                //dump($teacher_info);
                                // 教师数据
                                $teacher_data = [
                                    'id' => $teacher_info->id,// 原始ID***
                                    'teacher_id' => $teacher_info->id,// 冗余字段，便于下次更新带班等关系表信息
                                    'user_id' => $user_id,
                                    'school_id' => $school_id,
                                    'school_campus_id' => $teacher_info->school_district, // 之前导入的是校区时，校区主键ID没有变更
                                    'teacher_name' => $teacher_info->name,
                                    'is_psychology_teacher'=> $teacher_info->is_psych,
//                                    'created_at' => date('Y-m-d H:i:s',time()),
//                                    'updated_at' => date('Y-m-d H:i:s',time()),
                                ];
                                // 存储教师信息
                                DB::table('teachers')->insert($teacher_data);
                            }else{
                                dump('教师信息不存在:'.$item_member->id);
                            }
                        }
                    }catch (QueryException $e){
                        if($e->getCode() == 23000){
                            dump('用户名重复：'.$item_member->username);
                        }
                    }
                    // 教务角色到此结束
            });
        });
    }
}
