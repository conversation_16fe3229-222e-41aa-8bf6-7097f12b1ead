<?php

namespace Database\Seeders\assessment\subject;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
ini_set('memory_limit', '1024M');

class AnswersSeeder extends Seeder
{
    protected int $school_id;
    protected string $connect = 'mysql_prod';

    public function __construct($schoolId)
    {
        $this->school_id = $schoolId;
    }

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 限制测评类型
        $assessmentIds = [24];
        $surveyIds = [433];
        $surveyToAssessment = [
            '433'=>24,
        ];
        // 查询新老问题id的关系
        $assessmentsubjectQuestionIds = DB::table('assessment_subject_questions')
            ->whereNotNull('old_question_id')
            ->select(['id', 'old_question_id'])
            ->pluck('id', 'old_question_id')
            ->toArray();

        // 查询assessment_task_assignment_id和session_id的关系
        $assessmentTaskAssignmentIds = DB::table('assessment_schedules as schedules')
            ->join('assessment_tasks as tasks', 'schedules.id', '=', 'tasks.assessment_schedule_id')
            ->join('assessment_task_assignments as assignments', 'tasks.id', '=', 'assignments.assessment_task_id')
            ->where('schedules.school_id', $this->school_id)
            ->select(['assignments.id', 'assignments.old_session_id'])
            ->pluck('assignments.id', 'assignments.old_session_id')
            ->toArray();

        $studentAnswerList = DB::connection($this->connect)
            ->table('assessment_schedules as schedules')
            ->join('assessment_tasks as tasks', 'schedules.id', '=', 'tasks.assessment_schedule_id')
            ->join('survey_user_session as session', function($join) {
                $join->on('session.survey_id', '=', 'tasks.old_survey_id')
                    ->on('session.grade_id', '=', 'tasks.old_grade_id')
                    ->on('session.times', '=', 'tasks.old_times');
            })
            ->join('survey_user_answer as answer', 'session.session_id', '=', 'answer.session')
            ->whereIn('tasks.assessment_id', $assessmentIds)
            ->where('schedules.school_id', $this->school_id)
            ->whereIn('answer.survey_id', $surveyIds)
            ->where('session.is_delete', 0)
            ->where('session.is_abnormal', 0)
            ->where('session.time_error', 0)
            ->whereNotNull('session.result')
            ->whereNotNull('answer.input')
            ->select([
                'answer.input',
                'session.session_id',
                'session.member_id',
                'session.school_id',
                'session.survey_id'
            ])
            ->get()->toArray();

        $batchSize = 1000; // 每批处理的数据量
        $answerData = [];

        foreach ($studentAnswerList as $item) {
            $surveyId = $item->survey_id; // 假设 survey_id 是从 session 表或相关表中获取的
            $optionstoletterForSurvey = self::getOptionLettersForSurveyId($surveyId); // 调用静态方法获取当前 surveyId 对应的选项字母映射

            $input = json_decode($item->input, true);

            foreach ($input as $key => $value) {
                $answerOption = $optionstoletterForSurvey[$value['select_num']];
                // 检查 $key 是否在 $assessmentsubjectQuestionIds 数组中,因为学科测评题目有删减和最初做的题目数不一致
                if (array_key_exists($key, $assessmentsubjectQuestionIds)) {
                    $answerData[] = [
                        'assessment_task_assignment_id' => $assessmentTaskAssignmentIds[$item->session_id],
                        'student_id' => $item->member_id,
                        'school_id' => $item->school_id,
                        'assessment_subject_question_id' => $assessmentsubjectQuestionIds[$key],
                        'answer' => $answerOption,
                        'assessment_id' => $surveyToAssessment[$item->survey_id],
                    ];
                }

                // 如果达到批次大小，则插入数据库并清空数组
                if (count($answerData) >= $batchSize) {
                    DB::transaction(function () use ($answerData) {
                        DB::table('assessment_subject_answers')->insert($answerData);
                    });
                    $answerData = []; // 清空数组以释放内存
                }
            }
        }
        if (!empty($answerData)) {
            DB::transaction(function () use ($answerData) {
                DB::table('assessment_subject_answers')->insert($answerData);
            });
        }
    }
    /**
     * 获取给定 surveyId 的选项字母映射
     *
     * @param int $surveyId
     * @return array
     */
    public static function getOptionLettersForSurveyId(int $surveyId): array
    {
        switch ($surveyId) {
            case 433:
                return [
                    1 => 'A',
                    2 => 'B',
                    3 => 'C',
                    4 => 'D',
                    5 => 'E',
                ];
            default:
                return []; // 或者返回一个默认映射
        }
    }

}
