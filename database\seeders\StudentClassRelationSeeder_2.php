<?php

namespace Database\Seeders;

use App\Models\School\System\Claass;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

/**
 * 学生班级关系数据填充
 */
class StudentClassRelationSeeder_2 extends Seeder
{
    protected int $school_id;
    protected string $connect = 'mysql_prod';

    public function __construct($schoolId)
    {
        $this->school_id = $schoolId;
    }
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $school_id = $this->school_id;
        $total = 0;
        $num = 1;
        $batchSize = 1000;
        $student_class_data = [];
        $error_student_data = [];

        // 查询这个学校班级名称处理后的原始班级信息列表
        $class_list = DB::table('classes_old')->select('class_id', 'class_name', 'class_name3', 'school_campus_id', 'grade_id')
            ->where('school_id', $school_id)->get();

        // 查询旧的班级列表
        $student_list = DB::connection($this->connect)->table('student_convert')
            ->where('school_id',$school_id)
            ->orderBy('member_id')
            ->get()->each(function ($item) use ($school_id, &$num, &$total, &$batchSize, &$student_class_data, &$class_list, &$error_student_data){
                // 根据$item->student_ids 用,号分割成数组
                $student_ids = explode(',', $item->student_ids);
                $grade_years = explode(',', $item->grade_years);
                $grade_sorts = explode(',', $item->grade_sorts);
                $class_names = explode(',', $item->class_names);

//                // 用于测试排查错误的学生数据
//                if($item->member_id===880268){
//                    echo '班级名称：' . $item->class_names . "\n";
//                }

                // 带下标循环学生ID
                for ($i = 0; $i < count($student_ids); $i++) {
                    $class_name = $class_names[$i];
                    // 截取字符串，从出现第一个下划线_到最后的字符
                    $first_underscore_position = strpos($class_name, '_');
                    if ($first_underscore_position !== false) {
                        $class_name = substr($class_name, $first_underscore_position + 1);
                    }
                    $class_old = null;
                    foreach ($class_list as $class){
                        $grade_sort = $grade_sorts[$i];
                        if (strpos($grade_sort, '6.5')){
                            $grade_sort = 6;
                        }else{
                            $grade_sort = intval($grade_sort);
                        }

//                          // 用于测试排查错误的学生数据
//                        if ($class->class_name == '一班') {
//                            echo '班级名称：' . $class->class_name . "\n";
//                        }


                        if ($class->class_name == $class_name && $class->grade_id == $grade_sort && $class->school_campus_id == $item->school_district) {
                            $class_old = $class;
                            break;
                        }
                    }
                    if (!$class_old) {
                        $error_student_data[] = [
                            'school_id' => $school_id,
                            'school_campus_id' => $item->school_district,
                            'old_student_id' => $student_ids[$i],
                            'member_id' => $item->member_id,
                            'student_ids' => $item->student_ids,
                            'grade_years' => $item->grade_years,
                            'grade_sorts' => $item->grade_sorts,
                            'class_names' => $item->class_names
                        ];
                        continue;
//                        dd("学校ID：" . $school_id . "，迁移{$total}条学生班级关系数据错误：{$class_name}");
                    }

                    // 组装学生班级关系数据
                    $student_class_data[] = [
                        'old_student_id' => $student_ids[$i],
                        'student_id' => $item->member_id,
                        'class_id' => $class_old->class_id,
                        'class_name' => $class_old->class_name3,
                        'school_year' => intVal($grade_years[0]) + $i,
//                        'created_at' => date('Y-m-d H:i:s'),
//                        'updated_at' => date('Y-m-d H:i:s')
                    ];

                    $total = $total + 1;
                }

                $count = count($student_class_data);
                // 当数据量达到100条时进行插入
                if ($count >= $batchSize) {
                    DB::table('student_classes')->insert($student_class_data);
                    $student_class_data = []; // 清空数组

                    echo "学校ID：{$school_id}，迁移第 {$num} 批 {$count} 条学生班级关系数据 " . date('Y-m-d H:i:s') . "\n";

                    $num = $num + 1;
                }

                $error_count = count($error_student_data);
                if ($error_count >= 100) {
                    DB::table('student_classes_error')->insert($error_student_data);
                    $error_student_data = []; // 清空数组
                    echo "学校ID：{$school_id}，迁移学生班级关系数据出错 {$error_count} 条 " . date('Y-m-d H:i:s') . "\n";
                }

            });

        $count = count($student_class_data);
        // 最后一次插入剩余的数据
        if ($count > 0) {
            DB::table('student_classes')->insert($student_class_data);
            echo "学校ID：{$school_id}，迁移第 {$num} 批 {$count} 条学生班级关系数据 " . date('Y-m-d H:i:s') . "\n";
        }

        $error_count = count($error_student_data);
        if ($error_count > 0) {
            DB::table('student_classes_error')->insert($error_student_data);
            echo "学校ID：{$school_id}，迁移学生班级关系数据出错 {$error_count} 条 " . date('Y-m-d H:i:s') . "\n";
        }

        echo "学校ID：{$school_id}，共迁移{$total}条学生班级关系数据" . "\n";
    }


}
