<?php

namespace Database\Seeders\assessment\subject;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class TaskAssignmentsSeeder extends Seeder
{
    protected int $school_id;

    protected string $connect = 'mysql_prod';

    private const SURVEYIDTOASSESSMENTID = [
        433 => 24,
    ];

    public function __construct($schoolId)
    {
        $this->school_id = $schoolId;
    }
    public function run(): void
    {
        // 学科测评
        $assessmentIds = [24];
        $surveyIds = [433];
        // 查询学生测评记录
        $studentSurveyList = DB::connection($this->connect)
        ->table('assessment_schedules as schedules')
        ->join('assessment_tasks as tasks', 'schedules.id', '=', 'tasks.assessment_schedule_id')
        ->join('survey_user_session as session', function($join) {
            $join->on('session.survey_id', '=', 'tasks.old_survey_id')
                ->on('session.grade_id', '=', 'tasks.old_grade_id')
                ->on('session.times', '=', 'tasks.old_times');
        })
        ->whereIn('tasks.assessment_id', $assessmentIds)
        ->where('schedules.school_id', $this->school_id)
        ->where('session.school_id', $this->school_id)
        ->whereIn('session.survey_id', $surveyIds)
        ->where('session.is_delete', 0)
        ->where('session.is_abnormal', 0)
        ->where('session.time_error', 0)
        ->where('session.result', '!=', '')
        ->whereNotNull('session.result')
        ->select([
            'tasks.id',
            'tasks.assessment_id',
            'session.session_id',
            'session.member_id',
            'session.student_id',
            'session.create_time',
            'session.used_time',
            'session.result',
            'session.survey_id',
            'session.pdf_url',
            'session.school_id',
        ])
        ->get();
        // 优化查询效率
        $oldStudentIds = $studentSurveyList->pluck('student_id')->unique()->values()->toArray();
        $studentClassIdArr = DB::table('student_classes')
        ->whereIn('old_student_id', $oldStudentIds)
        ->select(['id', 'old_student_id'])
        ->pluck('id', 'old_student_id')
        ->toArray();

        // 使用集合方法优化数据处理
        $surveyToAssessmentMap = self::SURVEYIDTOASSESSMENTID; // 将常量赋值给变量
        $assignmentsData = $studentSurveyList->map(function($item) use ($studentClassIdArr,$surveyToAssessmentMap) {
        return [
            'old_session_id' => $item->session_id,
            'school_id' => $item->school_id,
            'assessment_task_id' => $item->id,
            'student_class_id' => $studentClassIdArr[$item->student_id] ?? 0,
            'student_id' => $item->member_id,
            'user_id' => $item->member_id,
            'duration' => $item->used_time,
            'results' => $item->result,
            'status' => 2,
            'assessment_id' => $surveyToAssessmentMap[$item->survey_id],
            'pdf_url' => $item->pdf_url,
            'created_at' => $item->create_time,
        ];
        })->toArray();

        // 使用事务和 chunk 插入大量数据
        DB::transaction(function () use ($assignmentsData) {
            collect($assignmentsData)->chunk(1000)->each(function ($chunk) {
                DB::table('assessment_task_assignments')->insert($chunk->toArray());
            });
        });
        // 使用更好的日志记录方式
        Log::info('career assignments seeding completed', [
            'career_total_records' => count($assignmentsData),
            'career_last_task_id' => end($assignmentsData)['assessment_task_id'] ?? null,
        ]);
    }
}
